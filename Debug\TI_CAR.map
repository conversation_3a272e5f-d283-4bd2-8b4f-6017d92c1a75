******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Mon Jul 14 21:52:40 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006e79


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009148  00016eb8  R  X
  SRAM                  20200000   00008000  00000645  000079bb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009148   00009148    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000079c0   000079c0    r-x .text
  00007a80    00007a80    00001680   00001680    r-- .rodata
  00009100    00009100    00000048   00000048    r-- .cinit
20200000    20200000    00000446   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000072   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000079c0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000290     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000137c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  000015f4    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000182c    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a58    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001c84    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001ea4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002098    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002274    000001b0     Task.o (.text.Task_Start)
                  00002424    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025c4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002756    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002758    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000028e0    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a58    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002bc8    00000150     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d18    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002e5c    00000144     PID.o (.text.PID_Prosc)
                  00002fa0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000030dc    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003210    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003344    00000130     OLED.o (.text.OLED_ShowChar)
                  00003474    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035a4    00000128     inv_mpu.o (.text.mpu_init)
                  000036cc    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000037f0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003910    00000110     OLED.o (.text.OLED_Init)
                  00003a20    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003b2c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003c34    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003d38    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003e38    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003f24    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004008    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000040ec    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000041c8    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000042a0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004378    000000d4     inv_mpu.o (.text.set_int_enable)
                  0000444c    000000d0     Task_App.o (.text.Task_Init)
                  0000451c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000045ec    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000046b0    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004774    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004830    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  000048e8    000000b4     Task.o (.text.Task_Add)
                  0000499c    000000ac     Motor.o (.text.Motor_SetPWM)
                  00004a48    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004af4    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004ba0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004c42    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004c44    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004ce4    000000a0     Task_App.o (.text.Task_Motor)
                  00004d84    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004e20    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004eb8    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004f50    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00004fe6    00000002     --HOLE-- [fill = 0]
                  00004fe8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00005074    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005100    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000518c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005218    00000088     Task_App.o (.text.Task_Key)
                  000052a0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005324    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000053a8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000542a    00000002     --HOLE-- [fill = 0]
                  0000542c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000054a8    00000074     Task_App.o (.text.Task_OLED)
                  0000551c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005590    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005604    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005678    0000006e     OLED.o (.text.OLED_ShowString)
                  000056e6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005750    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000057b8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000581e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005884    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000058e8    00000064     Motor.o (.text.Motor_Start)
                  0000594c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000059b0    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005a14    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005a76    00000002     --HOLE-- [fill = 0]
                  00005a78    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005ada    00000002     --HOLE-- [fill = 0]
                  00005adc    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005b3c    00000060     Key_Led.o (.text.Key_Read)
                  00005b9c    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005bfc    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005c5c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005cba    00000002     --HOLE-- [fill = 0]
                  00005cbc    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005d18    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005d74    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005dd0    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005e2c    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005e88    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00005ee0    00000058     Serial.o (.text.Serial_Init)
                  00005f38    00000058     Task_App.o (.text.Task_IdleFunction)
                  00005f90    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00005fe8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006040    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006096    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000060e8    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006138    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006188    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000061d8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006224    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006270    0000004c     OLED.o (.text.OLED_Printf)
                  000062bc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006306    00000002     --HOLE-- [fill = 0]
                  00006308    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006350    00000048     Interrupt.o (.text.Interrupt_Init)
                  00006398    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000063e0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006428    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006470    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000064b4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000064f8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  0000653c    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006580    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000065c0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006600    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006640    0000003e     Task.o (.text.Task_CMP)
                  0000667e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000066bc    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000066f8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006734    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006770    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000067ac    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000067e8    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006824    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006860    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000689c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000068d8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006912    00000002     --HOLE-- [fill = 0]
                  00006914    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000694e    00000002     --HOLE-- [fill = 0]
                  00006950    00000038     Task_App.o (.text.Task_LED)
                  00006988    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000069c0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000069f4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006a28    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006a5c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006a90    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006ac2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006af4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006b24    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006b54    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006b84    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006bb4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006be4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006c10    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006c3c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006c68    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00006c94    0000002a     PID.o (.text.PID_Init)
                  00006cbe    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006ce6    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006d0e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006d36    00000002     --HOLE-- [fill = 0]
                  00006d38    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006d60    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006d88    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006db0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006dd8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006e00    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00006e28    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006e50    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006e78    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006ea0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00006ec6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00006eec    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00006f12    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00006f38    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00006f5c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00006f80    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006fa2    00000002     --HOLE-- [fill = 0]
                  00006fa4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006fc4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00006fe4    00000020     SysTick.o (.text.Delay)
                  00007004    00000020     main.o (.text.main)
                  00007024    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007044    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007062    00000002     --HOLE-- [fill = 0]
                  00007064    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007082    00000002     --HOLE-- [fill = 0]
                  00007084    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000070a0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000070bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000070d8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000070f4    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007110    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000712c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007148    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007164    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007180    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000719c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000071b8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000071d4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000071f0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000720c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007228    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007244    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007260    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000727c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007298    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000072b0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000072c8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000072e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000072f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007310    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007328    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007340    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007370    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007388    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000073a0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000073b8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000073d0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000073e8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007400    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007418    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007430    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007448    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007460    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007478    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007490    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000074a8    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000074c0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000074d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000074f0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007508    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007520    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007538    00000018     OLED.o (.text.DL_I2C_reset)
                  00007550    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007568    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007580    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007598    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000075b0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000075c8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000075e0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000075f8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007610    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007628    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007640    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007658    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007670    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007688    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000076a0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000076b8    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000076d0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000076e6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000076fc    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007712    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007728    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  0000773e    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007754    00000016     SysTick.o (.text.SysGetTick)
                  0000776a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007780    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007794    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000077a8    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000077bc    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000077d0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000077e4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000077f8    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  0000780c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007820    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007834    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007848    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000785c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007870    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007884    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007898    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000078ac    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000078c0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000078d2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000078e4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000078f6    00000002     --HOLE-- [fill = 0]
                  000078f8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007908    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007918    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007928    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007938    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007946    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007954    0000000e     MPU6050.o (.text.tap_cb)
                  00007962    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007970    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000797c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007988    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007992    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000799c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000079ac    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000079b6    00000002     --HOLE-- [fill = 0]
                  000079b8    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000079c8    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000079d2    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000079dc    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000079e6    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000079f0    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007a00    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00007a0a    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007a14    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007a1c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007a24    00000008     Task_App.o (.text.Task_Serial)
                  00007a2c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007a34    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007a3c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007a42    00000002     --HOLE-- [fill = 0]
                  00007a44    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007a54    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007a5a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007a5e    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007a62    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007a66    00000002     --HOLE-- [fill = 0]
                  00007a68    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007a78    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007a7c    00000004            : exit.c.obj (.text:abort)

.cinit     0    00009100    00000048     
                  00009100    00000020     (.cinit..data.load) [load image, compression = lzss]
                  00009120    0000000c     (__TI_handler_table)
                  0000912c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009134    00000010     (__TI_cinit_table)
                  00009144    00000004     --HOLE-- [fill = 0]

.rodata    0    00007a80    00001680     
                  00007a80    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008676    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00008c66    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00008e8e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008e90    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008f91    00000007     Task_App.o (.rodata.str1.136405643080007560121)
                  00008f98    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008fd8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009000    00000028     inv_mpu.o (.rodata.test)
                  00009028    0000001e     inv_mpu.o (.rodata.reg)
                  00009046    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009048    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009060    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009078    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00009089    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  0000909a    0000000c     inv_mpu.o (.rodata.hw)
                  000090a6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000090b0    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  000090b8    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000090c0    00000008     Task_App.o (.rodata.str1.157702741485139367601)
                  000090c8    00000008     Task_App.o (.rodata.str1.182657883079055368591)
                  000090d0    00000008     Task_App.o (.rodata.str1.87978995337490384161)
                  000090d8    00000008     Task_App.o (.rodata.str1.97872905622636903301)
                  000090e0    00000006     Task_App.o (.rodata.str1.115332825834609149281)
                  000090e6    00000005     Task_App.o (.rodata.str1.134609064190095881641)
                  000090eb    00000004     Task_App.o (.rodata.str1.171900814140190138471)
                  000090ef    00000004     Task_App.o (.rodata.str1.67400646179352630301)
                  000090f3    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  000090f6    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  000090f9    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000090fb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:Data_MotorPID)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000072     UNINITIALIZED
                  202003d4    0000002c     inv_mpu.o (.data.st)
                  20200400    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200410    0000000e     MPU6050.o (.data.hal)
                  2020041e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200427    00000001     Task_App.o (.data.Flag_LED)
                  20200428    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200430    00000004     Task_App.o (.data.Data_MotorPWM_Duty)
                  20200434    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200438    00000004     SysTick.o (.data.delayTick)
                  2020043c    00000004     SysTick.o (.data.uwTick)
                  20200440    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200442    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200443    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200444    00000001     Task.o (.data.Task_Num)
                  20200445    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3446    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3486    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     816     58        192    
       Interrupt.o                    562     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1378    58        198    
                                                               
    .\BSP\Src\
       MPU6050.o                      2472    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1854    0         0      
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       Motor.o                        432     0         0      
       PID.o                          366     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         6314    2072      831    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    D:/TI/CCSTUDIO_Theia/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8218    355       4      
                                                               
    D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2918    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       68        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31136   6015      1605   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009134 records: 2, size/record: 8, table size: 16
	.data: load addr=00009100, load size=00000020 bytes, run addr=202003d4, run size=00000072 bytes, compression=lzss
	.bss: load addr=0000912c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009120 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025c5     0000799c     0000799a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003f25     000079b8     000079b4   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000079d0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000079e4          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007a1a          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007a58          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003a21     000079f0     000079ee   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025cf     00007a44     00007a40   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007a60          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006e79     00007a68     00007a62   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007a5b  ADC0_IRQHandler                      
00007a5b  ADC1_IRQHandler                      
00007a5b  AES_IRQHandler                       
00007a7c  C$$EXIT                              
00007a5b  CANFD0_IRQHandler                    
00007a5b  DAC0_IRQHandler                      
00007989  DL_Common_delayCycles                
000061d9  DL_DMA_initChannel                   
00005c5d  DL_I2C_fillControllerTXFIFO          
00006771  DL_I2C_flushControllerTXFIFO         
00006f13  DL_I2C_setClockConfig                
000040ed  DL_SYSCTL_configSYSPLL               
00005885  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006471  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003c35  DL_Timer_initFourCCPWMMode           
00007229  DL_Timer_setCaptCompUpdateMethod     
000075f9  DL_Timer_setCaptureCompareOutCtl     
00007909  DL_Timer_setCaptureCompareValue      
00007245  DL_Timer_setClockConfig              
00006309  DL_UART_init                         
000078c1  DL_UART_setClockConfig               
00007a5b  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
20200428  Data_MotorEncoder                    
202002f0  Data_MotorPID                        
20200430  Data_MotorPWM_Duty                   
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
00007a5b  Default_Handler                      
00006fe5  Delay                                
202003c8  ExISR_Flag                           
20200427  Flag_LED                             
20200442  Flag_MPU6050_Ready                   
00007a5b  GROUP0_IRQHandler                    
00002bc9  GROUP1_IRQHandler                    
00007a5b  HardFault_Handler                    
00007a5b  I2C0_IRQHandler                      
00007a5b  I2C1_IRQHandler                      
000056e7  I2C_OLED_Clear                       
000067e9  I2C_OLED_Set_Pos                     
00004e21  I2C_OLED_WR_Byte                     
00005add  I2C_OLED_i2c_sda_unlock              
00006351  Interrupt_Init                       
00005b3d  Key_Read                             
00002d19  MPU6050_Init                         
00005cbd  Motor_SetDirc                        
0000499d  Motor_SetPWM                         
000058e9  Motor_Start                          
00007a5b  NMI_Handler                          
00003911  OLED_Init                            
00006271  OLED_Printf                          
00003345  OLED_ShowChar                        
00005679  OLED_ShowString                      
00006c95  PID_Init                             
00002e5d  PID_Prosc                            
00007a5b  PendSV_Handler                       
00007a5b  RTC_IRQHandler                       
0000182d  Read_Quad                            
00007a63  Reset_Handler                        
00007a5b  SPI0_IRQHandler                      
00007a5b  SPI1_IRQHandler                      
00007a5b  SVC_Handler                          
00006b25  SYSCFG_DL_DMA_CH_RX_init             
000076a1  SYSCFG_DL_DMA_CH_TX_init             
00007971  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00005e89  SYSCFG_DL_I2C_MPU6050_init           
0000594d  SYSCFG_DL_I2C_OLED_init              
00004fe9  SYSCFG_DL_MotorBack_init             
00005075  SYSCFG_DL_MotorFront_init            
00005d19  SYSCFG_DL_SYSCTL_init                
00007919  SYSCFG_DL_SYSTICK_init               
000052a1  SYSCFG_DL_UART0_init                 
00006be5  SYSCFG_DL_init                       
00004c45  SYSCFG_DL_initPower                  
00005ee1  Serial_Init                          
20200000  Serial_RxData                        
00007755  SysGetTick                           
00007a1d  SysTick_Handler                      
00006e29  SysTick_Increasment                  
0000797d  Sys_GetTick                          
00007a5b  TIMA0_IRQHandler                     
00007a5b  TIMA1_IRQHandler                     
00007a5b  TIMG0_IRQHandler                     
00007a5b  TIMG12_IRQHandler                    
00007a5b  TIMG6_IRQHandler                     
00007a5b  TIMG7_IRQHandler                     
00007a5b  TIMG8_IRQHandler                     
000078d3  TI_memcpy_small                      
00007963  TI_memset_small                      
000048e9  Task_Add                             
00005f39  Task_IdleFunction                    
0000444d  Task_Init                            
00005219  Task_Key                             
00006951  Task_LED                             
00004ce5  Task_Motor                           
000054a9  Task_OLED                            
00007a25  Task_Serial                          
00002275  Task_Start                           
00007a5b  UART0_IRQHandler                     
00007a5b  UART1_IRQHandler                     
00007a5b  UART2_IRQHandler                     
00007a5b  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009134  __TI_CINIT_Base                      
00009144  __TI_CINIT_Limit                     
00009144  __TI_CINIT_Warm                      
00009120  __TI_Handler_Table_Base              
0000912c  __TI_Handler_Table_Limit             
0000689d  __TI_auto_init_nobinit_nopinit       
0000542d  __TI_decompress_lzss                 
000078e5  __TI_decompress_none                 
00005f91  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000776b  __TI_zero_init_nomemset              
000025cf  __adddf3                             
000042ab  __addsf3                             
00008e90  __aeabi_ctype_table_                 
00008e90  __aeabi_ctype_table_C                
00005591  __aeabi_d2f                          
000062bd  __aeabi_d2iz                         
000025cf  __aeabi_dadd                         
00005a15  __aeabi_dcmpeq                       
00005a51  __aeabi_dcmpge                       
00005a65  __aeabi_dcmpgt                       
00005a3d  __aeabi_dcmple                       
00005a29  __aeabi_dcmplt                       
00003a21  __aeabi_ddiv                         
00003f25  __aeabi_dmul                         
000025c5  __aeabi_dsub                         
20200434  __aeabi_errno                        
00007a2d  __aeabi_errno_addr                   
000065c1  __aeabi_f2d                          
00006989  __aeabi_f2iz                         
000042ab  __aeabi_fadd                         
00005a79  __aeabi_fcmpeq                       
00005ab5  __aeabi_fcmpge                       
00005ac9  __aeabi_fcmpgt                       
00005aa1  __aeabi_fcmple                       
00005a8d  __aeabi_fcmplt                       
000053a9  __aeabi_fdiv                         
00005101  __aeabi_fmul                         
000042a1  __aeabi_fsub                         
00006c3d  __aeabi_i2d                          
00006825  __aeabi_i2f                          
00006041  __aeabi_idiv                         
00002757  __aeabi_idiv0                        
00006041  __aeabi_idivmod                      
00004c43  __aeabi_ldiv0                        
00007065  __aeabi_llsl                         
00006f5d  __aeabi_lmul                         
00007a35  __aeabi_memcpy                       
00007a35  __aeabi_memcpy4                      
00007a35  __aeabi_memcpy8                      
00007939  __aeabi_memset                       
00007939  __aeabi_memset4                      
00007939  __aeabi_memset8                      
00006e51  __aeabi_ui2f                         
00006581  __aeabi_uidiv                        
00006581  __aeabi_uidivmod                     
00007871  __aeabi_uldivmod                     
00007065  __ashldi3                            
ffffffff  __binit__                            
00005751  __cmpdf2                             
000068d9  __cmpsf2                             
00003a21  __divdf3                             
000053a9  __divsf3                             
00005751  __eqdf2                              
000068d9  __eqsf2                              
000065c1  __extendsfdf2                        
000062bd  __fixdfsi                            
00006989  __fixsfsi                            
00006c3d  __floatsidf                          
00006825  __floatsisf                          
00006e51  __floatunsisf                        
0000551d  __gedf2                              
00006861  __gesf2                              
0000551d  __gtdf2                              
00006861  __gtsf2                              
00005751  __ledf2                              
000068d9  __lesf2                              
00005751  __ltdf2                              
000068d9  __ltsf2                              
UNDEFED   __mpu_init                           
00003f25  __muldf3                             
00006f5d  __muldi3                             
00006915  __muldsi3                            
00005101  __mulsf3                             
00005751  __nedf2                              
000068d9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025c5  __subdf3                             
000042a1  __subsf3                             
00005591  __truncdfsf2                         
00004ba1  __udivmoddi4                         
00006e79  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007a79  _system_pre_init                     
00007a7d  abort                                
00008c66  asc2_0806                            
00008676  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002759  atan2                                
00002759  atan2l                               
00000df5  atanl                                
00006601  atoi                                 
ffffffff  binit                                
20200438  delayTick                            
00006399  dmp_enable_6x_lp_quat                
0000137d  dmp_enable_feature                   
00005b9d  dmp_enable_gyro_cal                  
000063e1  dmp_enable_lp_quat                   
0000727d  dmp_load_motion_driver_firmware      
00001ea5  dmp_read_fifo                        
00007885  dmp_register_android_orient_cb       
00007899  dmp_register_tap_cb                  
00004eb9  dmp_set_fifo_rate                    
000028e1  dmp_set_orientation                  
000064b5  dmp_set_shake_reject_thresh          
00006a91  dmp_set_shake_reject_time            
00006ac3  dmp_set_shake_reject_timeout         
0000581f  dmp_set_tap_axes                     
000064f9  dmp_set_tap_count                    
000015f5  dmp_set_tap_thresh                   
00006b85  dmp_set_tap_time                     
00006bb5  dmp_set_tap_time_multi               
20200445  enable_group1_irq                    
00005d75  frexp                                
00005d75  frexpl                               
0000909a  hw                                   
00000000  interruptVectors                     
000041c9  ldexp                                
000041c9  ldexpl                               
00007005  main                                 
00006f81  memccpy                              
00007025  memcmp                               
202003d2  more                                 
000059b1  mpu6050_i2c_sda_unlock               
00004775  mpu_configure_fifo                   
00005605  mpu_get_accel_fsr                    
00005bfd  mpu_get_gyro_fsr                     
00006a5d  mpu_get_sample_rate                  
000035a5  mpu_init                             
000036cd  mpu_load_firmware                    
00003d39  mpu_lp_accel_mode                    
00003b2d  mpu_read_fifo_stream                 
00004a49  mpu_read_mem                         
00001a59  mpu_reset_fifo                       
00004009  mpu_set_accel_fsr                    
00002425  mpu_set_bypass                       
00004831  mpu_set_dmp_state                    
000045ed  mpu_set_gyro_fsr                     
00004d85  mpu_set_int_latched                  
0000451d  mpu_set_lpf                          
00003e39  mpu_set_sample_rate                  
00003475  mpu_set_sensors                      
00004af5  mpu_write_mem                        
000030dd  mspm0_i2c_read                       
000046b1  mspm0_i2c_write                      
00003211  qsort                                
202003a0  quat                                 
00009028  reg                                  
000041c9  scalbn                               
000041c9  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002a59  sqrt                                 
00002a59  sqrtl                                
00009000  test                                 
2020043c  uwTick                               
00006c69  vsprintf                             
00007929  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
0000137d  dmp_enable_feature                   
000015f5  dmp_set_tap_thresh                   
0000182d  Read_Quad                            
00001a59  mpu_reset_fifo                       
00001ea5  dmp_read_fifo                        
00002275  Task_Start                           
00002425  mpu_set_bypass                       
000025c5  __aeabi_dsub                         
000025c5  __subdf3                             
000025cf  __adddf3                             
000025cf  __aeabi_dadd                         
00002757  __aeabi_idiv0                        
00002759  atan2                                
00002759  atan2l                               
000028e1  dmp_set_orientation                  
00002a59  sqrt                                 
00002a59  sqrtl                                
00002bc9  GROUP1_IRQHandler                    
00002d19  MPU6050_Init                         
00002e5d  PID_Prosc                            
000030dd  mspm0_i2c_read                       
00003211  qsort                                
00003345  OLED_ShowChar                        
00003475  mpu_set_sensors                      
000035a5  mpu_init                             
000036cd  mpu_load_firmware                    
00003911  OLED_Init                            
00003a21  __aeabi_ddiv                         
00003a21  __divdf3                             
00003b2d  mpu_read_fifo_stream                 
00003c35  DL_Timer_initFourCCPWMMode           
00003d39  mpu_lp_accel_mode                    
00003e39  mpu_set_sample_rate                  
00003f25  __aeabi_dmul                         
00003f25  __muldf3                             
00004009  mpu_set_accel_fsr                    
000040ed  DL_SYSCTL_configSYSPLL               
000041c9  ldexp                                
000041c9  ldexpl                               
000041c9  scalbn                               
000041c9  scalbnl                              
000042a1  __aeabi_fsub                         
000042a1  __subsf3                             
000042ab  __addsf3                             
000042ab  __aeabi_fadd                         
0000444d  Task_Init                            
0000451d  mpu_set_lpf                          
000045ed  mpu_set_gyro_fsr                     
000046b1  mspm0_i2c_write                      
00004775  mpu_configure_fifo                   
00004831  mpu_set_dmp_state                    
000048e9  Task_Add                             
0000499d  Motor_SetPWM                         
00004a49  mpu_read_mem                         
00004af5  mpu_write_mem                        
00004ba1  __udivmoddi4                         
00004c43  __aeabi_ldiv0                        
00004c45  SYSCFG_DL_initPower                  
00004ce5  Task_Motor                           
00004d85  mpu_set_int_latched                  
00004e21  I2C_OLED_WR_Byte                     
00004eb9  dmp_set_fifo_rate                    
00004fe9  SYSCFG_DL_MotorBack_init             
00005075  SYSCFG_DL_MotorFront_init            
00005101  __aeabi_fmul                         
00005101  __mulsf3                             
00005219  Task_Key                             
000052a1  SYSCFG_DL_UART0_init                 
000053a9  __aeabi_fdiv                         
000053a9  __divsf3                             
0000542d  __TI_decompress_lzss                 
000054a9  Task_OLED                            
0000551d  __gedf2                              
0000551d  __gtdf2                              
00005591  __aeabi_d2f                          
00005591  __truncdfsf2                         
00005605  mpu_get_accel_fsr                    
00005679  OLED_ShowString                      
000056e7  I2C_OLED_Clear                       
00005751  __cmpdf2                             
00005751  __eqdf2                              
00005751  __ledf2                              
00005751  __ltdf2                              
00005751  __nedf2                              
0000581f  dmp_set_tap_axes                     
00005885  DL_SYSCTL_setHFCLKSourceHFXTParams   
000058e9  Motor_Start                          
0000594d  SYSCFG_DL_I2C_OLED_init              
000059b1  mpu6050_i2c_sda_unlock               
00005a15  __aeabi_dcmpeq                       
00005a29  __aeabi_dcmplt                       
00005a3d  __aeabi_dcmple                       
00005a51  __aeabi_dcmpge                       
00005a65  __aeabi_dcmpgt                       
00005a79  __aeabi_fcmpeq                       
00005a8d  __aeabi_fcmplt                       
00005aa1  __aeabi_fcmple                       
00005ab5  __aeabi_fcmpge                       
00005ac9  __aeabi_fcmpgt                       
00005add  I2C_OLED_i2c_sda_unlock              
00005b3d  Key_Read                             
00005b9d  dmp_enable_gyro_cal                  
00005bfd  mpu_get_gyro_fsr                     
00005c5d  DL_I2C_fillControllerTXFIFO          
00005cbd  Motor_SetDirc                        
00005d19  SYSCFG_DL_SYSCTL_init                
00005d75  frexp                                
00005d75  frexpl                               
00005e89  SYSCFG_DL_I2C_MPU6050_init           
00005ee1  Serial_Init                          
00005f39  Task_IdleFunction                    
00005f91  __TI_ltoa                            
00006041  __aeabi_idiv                         
00006041  __aeabi_idivmod                      
000061d9  DL_DMA_initChannel                   
00006271  OLED_Printf                          
000062bd  __aeabi_d2iz                         
000062bd  __fixdfsi                            
00006309  DL_UART_init                         
00006351  Interrupt_Init                       
00006399  dmp_enable_6x_lp_quat                
000063e1  dmp_enable_lp_quat                   
00006471  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000064b5  dmp_set_shake_reject_thresh          
000064f9  dmp_set_tap_count                    
00006581  __aeabi_uidiv                        
00006581  __aeabi_uidivmod                     
000065c1  __aeabi_f2d                          
000065c1  __extendsfdf2                        
00006601  atoi                                 
00006771  DL_I2C_flushControllerTXFIFO         
000067e9  I2C_OLED_Set_Pos                     
00006825  __aeabi_i2f                          
00006825  __floatsisf                          
00006861  __gesf2                              
00006861  __gtsf2                              
0000689d  __TI_auto_init_nobinit_nopinit       
000068d9  __cmpsf2                             
000068d9  __eqsf2                              
000068d9  __lesf2                              
000068d9  __ltsf2                              
000068d9  __nesf2                              
00006915  __muldsi3                            
00006951  Task_LED                             
00006989  __aeabi_f2iz                         
00006989  __fixsfsi                            
00006a5d  mpu_get_sample_rate                  
00006a91  dmp_set_shake_reject_time            
00006ac3  dmp_set_shake_reject_timeout         
00006b25  SYSCFG_DL_DMA_CH_RX_init             
00006b85  dmp_set_tap_time                     
00006bb5  dmp_set_tap_time_multi               
00006be5  SYSCFG_DL_init                       
00006c3d  __aeabi_i2d                          
00006c3d  __floatsidf                          
00006c69  vsprintf                             
00006c95  PID_Init                             
00006e29  SysTick_Increasment                  
00006e51  __aeabi_ui2f                         
00006e51  __floatunsisf                        
00006e79  _c_int00_noargs                      
00006f13  DL_I2C_setClockConfig                
00006f5d  __aeabi_lmul                         
00006f5d  __muldi3                             
00006f81  memccpy                              
00006fe5  Delay                                
00007005  main                                 
00007025  memcmp                               
00007065  __aeabi_llsl                         
00007065  __ashldi3                            
00007229  DL_Timer_setCaptCompUpdateMethod     
00007245  DL_Timer_setClockConfig              
0000727d  dmp_load_motion_driver_firmware      
000075f9  DL_Timer_setCaptureCompareOutCtl     
000076a1  SYSCFG_DL_DMA_CH_TX_init             
00007755  SysGetTick                           
0000776b  __TI_zero_init_nomemset              
00007871  __aeabi_uldivmod                     
00007885  dmp_register_android_orient_cb       
00007899  dmp_register_tap_cb                  
000078c1  DL_UART_setClockConfig               
000078d3  TI_memcpy_small                      
000078e5  __TI_decompress_none                 
00007909  DL_Timer_setCaptureCompareValue      
00007919  SYSCFG_DL_SYSTICK_init               
00007929  wcslen                               
00007939  __aeabi_memset                       
00007939  __aeabi_memset4                      
00007939  __aeabi_memset8                      
00007963  TI_memset_small                      
00007971  SYSCFG_DL_DMA_init                   
0000797d  Sys_GetTick                          
00007989  DL_Common_delayCycles                
00007a1d  SysTick_Handler                      
00007a25  Task_Serial                          
00007a2d  __aeabi_errno_addr                   
00007a35  __aeabi_memcpy                       
00007a35  __aeabi_memcpy4                      
00007a35  __aeabi_memcpy8                      
00007a5b  ADC0_IRQHandler                      
00007a5b  ADC1_IRQHandler                      
00007a5b  AES_IRQHandler                       
00007a5b  CANFD0_IRQHandler                    
00007a5b  DAC0_IRQHandler                      
00007a5b  DMA_IRQHandler                       
00007a5b  Default_Handler                      
00007a5b  GROUP0_IRQHandler                    
00007a5b  HardFault_Handler                    
00007a5b  I2C0_IRQHandler                      
00007a5b  I2C1_IRQHandler                      
00007a5b  NMI_Handler                          
00007a5b  PendSV_Handler                       
00007a5b  RTC_IRQHandler                       
00007a5b  SPI0_IRQHandler                      
00007a5b  SPI1_IRQHandler                      
00007a5b  SVC_Handler                          
00007a5b  TIMA0_IRQHandler                     
00007a5b  TIMA1_IRQHandler                     
00007a5b  TIMG0_IRQHandler                     
00007a5b  TIMG12_IRQHandler                    
00007a5b  TIMG6_IRQHandler                     
00007a5b  TIMG7_IRQHandler                     
00007a5b  TIMG8_IRQHandler                     
00007a5b  UART0_IRQHandler                     
00007a5b  UART1_IRQHandler                     
00007a5b  UART2_IRQHandler                     
00007a5b  UART3_IRQHandler                     
00007a63  Reset_Handler                        
00007a79  _system_pre_init                     
00007a7c  C$$EXIT                              
00007a7d  abort                                
00008676  asc2_1608                            
00008c66  asc2_0806                            
00008e90  __aeabi_ctype_table_                 
00008e90  __aeabi_ctype_table_C                
00009000  test                                 
00009028  reg                                  
0000909a  hw                                   
00009120  __TI_Handler_Table_Base              
0000912c  __TI_Handler_Table_Limit             
00009134  __TI_CINIT_Base                      
00009144  __TI_CINIT_Limit                     
00009144  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  Data_MotorPID                        
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
20200427  Flag_LED                             
20200428  Data_MotorEncoder                    
20200430  Data_MotorPWM_Duty                   
20200434  __aeabi_errno                        
20200438  delayTick                            
2020043c  uwTick                               
20200442  Flag_MPU6050_Ready                   
20200445  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[294 symbols]
