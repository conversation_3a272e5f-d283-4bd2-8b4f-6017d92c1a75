#include "PID_IQMath.h"

#define PID_IQ_DYNAMIC_KI_INDEX _IQ(0.3) //变速积分的计算值 值越小 KI作用越小
#define PID_IQ_KI_MAX_VALUE     _IQ(50) //积分项最大值
#define PID_IQ_OUT_MAX_VALUE    100.0f //PID 输出最大值

/**
 * @brief 初始化PID项
 * 
 * @param pid PID对象
 */
void PID_IQ_Init(PID_IQ_Def_t *pid)
{
    // PID系数初始化为0，实际使用时需要用户设置
    // pid->Kp = _IQ(0);
    // pid->Ki = _IQ(0);
    // pid->Kd = _IQ(0);

    // 清零所有状态变量
    pid->Acutal_Now = 0.0f; // 当前实际值(float)
    pid->Acutal_Last = _IQ(0); // 上次实际值(_iq)
    pid->Out = 0.0f; // 输出值(float)
    pid->Target = 0.0f; // 目标值(float)
    pid->Dif_Out = _IQ(0); // 微分项输出(_iq)
    pid->Err_Int = _IQ(0); // 积分累积(_iq)
    pid->Err_Last = _IQ(0); // 上次误差(_iq)
    pid->Err_Now = _IQ(0); // 当前误差(_iq)
}

/**
 * @brief PID处理逻辑
 * 
 * @param pid 处理PID对象
 */
void PID_IQ_Prosc(PID_IQ_Def_t *pid)
{
    // 保存历史数据
    pid->Err_Last = pid->Err_Now; // 保存上次误差
    pid->Acutal_Last = _IQ(pid->Acutal_Now); // 保存上次实际值(float→_iq)

    // 计算当前误差 (目标值 - 实际值)
    pid->Err_Now = _IQsub(_IQ(pid->Target), _IQ(pid->Acutal_Now));

    // 误差越大，积分作用越小，避免积分饱和
    _iq abs_err = _IQabs(pid->Err_Now); // 取误差绝对值
    _iq temp = _IQmpy(PID_IQ_DYNAMIC_KI_INDEX, abs_err); // 0.3 * |error|
    _iq C = _IQdiv(_IQ(1), _IQadd(temp, _IQ(1))); // C = 1/(0.3*|error| + 1)
    _iq err_weighted = _IQmpy(C, pid->Err_Now); // 加权误差 = C * error
    pid->Err_Int = _IQadd(pid->Err_Int, err_weighted); // 积分累积

    // 积分项限幅，防止积分饱和
    if (_IQtoF(pid->Err_Int) > _IQtoF(PID_IQ_KI_MAX_VALUE)) pid->Err_Int = PID_IQ_KI_MAX_VALUE;
    else if (_IQtoF(pid->Err_Int) < _IQtoF(-PID_IQ_KI_MAX_VALUE)) pid->Err_Int = -PID_IQ_KI_MAX_VALUE;

    // 使用实际值变化计算微分项，而非误差变化，避免设定值突变的冲击
    _iq actual_diff = _IQsub(_IQ(pid->Acutal_Now), pid->Acutal_Last);
    pid->Dif_Out = _IQmpy(-pid->Kd, actual_diff); // 微分项 = -Kd * Δ实际值

    _iq p_term = _IQmpy(pid->Kp, pid->Err_Now); // 比例项 = Kp * error
    _iq i_term = _IQmpy(pid->Ki, pid->Err_Int); // 积分项 = Ki * ∫error
    _iq out_iq = _IQadd(_IQadd(p_term, i_term), pid->Dif_Out); // PID = P + I + D

    // 转换为float类型并进行限幅
    pid->Out = _IQtoF(out_iq);
    if (pid->Out > PID_IQ_OUT_MAX_VALUE) pid->Out = PID_IQ_OUT_MAX_VALUE;
    if (pid->Out < -PID_IQ_OUT_MAX_VALUE) pid->Out = -PID_IQ_OUT_MAX_VALUE;
}