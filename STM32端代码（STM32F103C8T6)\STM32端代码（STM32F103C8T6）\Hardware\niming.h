#ifndef __NIMING_H
#define __NIMING_H
#include "stm32f10x.h"                  // Device header
#include "usart1.h"

#define BYTE0(dwTemp) (*(char *)(&dwTemp))
#define BYTE1(dwTemp) (*((char *)(&dwTemp) + 1))
#define BYTE2(dwTemp) (*((char *)(&dwTemp) + 2))
#define BYTE3(dwTemp) (*((char *)(&dwTemp) + 3))

void ANO_DT_Send_F1(uint16_t _a, uint16_t _b, uint16_t _c, uint16_t _d);
void USART2_SendArray(uint8_t *Array,uint16_t Length);
void USART2_SendByte(uint8_t Byte);




#endif
