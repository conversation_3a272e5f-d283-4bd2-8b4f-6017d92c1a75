#include "huidu.h"


void Huidu_GPIO_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
	
	GPIO_InitTypeDef GPIO_InitStructure0;
	GPIO_InitStructure0.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure0.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_1 | GPIO_Pin_0;
	GPIO_InitStructure0.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure0);
	
	GPIO_InitTypeDef GPIO_InitStructure1;
	GPIO_InitStructure1.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure1.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_6 | GPIO_Pin_5 | GPIO_Pin_4 | GPIO_Pin_3;
	GPIO_InitStructure1.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure1);
}

uint8_t Huidu_Receive1(void)
{
	if(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_10)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive2(void)
{
	if(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_1)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive3(void)
{
	if(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_0)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive4(void)
{
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_7)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive5(void)
{
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_6)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive6(void)
{
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_5)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive7(void)
{
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_4)==0)
	{
	return 0;
	}
		return 1;
}

uint8_t Huidu_Receive8(void)
{
	if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_3)==0)
	{
	return 0;
	}
		return 1;
}
