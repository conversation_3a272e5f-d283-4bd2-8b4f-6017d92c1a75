#include "Tracker.h"

bool Tracker_Read(uint8_t *tck_ptr, DEV_TCK_Def_t *dev_ptr)
{
    if (tck_ptr == NULL || dev_ptr == NULL) return false;

    /*获取循迹传感器的值*/
    *tck_ptr = DL_GPIO_readPins(Tracker__1_PORT, Tracker__1_PIN);
    *tck_ptr++ = DL_GPIO_readPins(Tracker__2_PORT, Tracker__2_PIN);
    *tck_ptr++ = DL_GPIO_readPins(Tracker__3_PORT, Tracker__3_PIN);
    *tck_ptr++ = DL_GPIO_readPins(Tracker__4_PORT, Tracker__4_PIN);
    *tck_ptr++ = DL_GPIO_readPins(Tracker__5_PORT, Tracker__5_PIN);
    *tck_ptr++ = DL_GPIO_readPins(Tracker__6_PORT, Tracker__6_PIN);
    // *tck_ptr++ = DL_GPIO_readPins(Tracker__7_PORT, Tracker__7_PIN);
    // *tck_ptr++ = DL_GPIO_readPins(Tracker__8_PORT, Tracker__8_PIN);

    /*判断当前的方向*/
    uint8_t track_cnt = 0, pos_1, pos_2;
    for (pos_1 = 0, pos_2 = 1; pos_1 < 8; pos_1++, pos_2++)
    {
        if (tck_ptr[pos_1] == TRACK_ON) track_cnt++;
        if (track_cnt == TRACK_WIDTH) break;
    }

    if (track_cnt < 2) return false;

    if (pos_1 == 4) //居中
    {
        *dev_ptr = DEV_MID;
    }
    else if (pos_1 < 4) //偏右
    {
        switch (pos_2)
        {
            case 4:
                *dev_ptr = DEV_RIG_1;
                return true;
            case 3:
                *dev_ptr = DEV_RIG_2;
                return true;
            case 2:
                *dev_ptr = DEV_RIG_3;
                return true;
            default:
                break;
        }
    }
    else if (pos_1 > 4) //偏左
    {
        switch (pos_2)
        {
            case 6:
                *dev_ptr = DEV_LEF_1;
                return true;
            case 7:
                *dev_ptr = DEV_LEF_2;
                return true;
            case 8:
                *dev_ptr = DEV_LEF_3;
                return true;
            default:
                break;
        }
    }

    return false;
}
