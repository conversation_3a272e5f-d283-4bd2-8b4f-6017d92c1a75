Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to myconfig.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    led.o(i.LED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_GREEN_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_GREEN_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_RED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_RED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_YELLOW_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_YELLOW_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    infrared.o(i.Infrafred_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    infrared.o(i.Infrafred_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    infrared.o(i.LoadOrNot) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    infrared.o(i.LoadOrNot) refers to delay.o(i.Delay_ms) for Delay_ms
    infrared.o(i.LoadOrNot) refers to delay.o(i.Delay_us) for Delay_us
    infrared.o(i.LoadOrNot) refers to infrared.o(.data) for load_flag
    motor.o(i.Load_Motor_PWM) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(i.Load_Motor_PWM) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(i.Motor_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.set_motor_disable) refers to stm32f10x_tim.o(i.TIM_CCxCmd) for TIM_CCxCmd
    motor.o(i.set_motor_disable) refers to myconfig.o(.bss) for Flag
    motor.o(i.set_motor_enable) refers to stm32f10x_tim.o(i.TIM_CCxCmd) for TIM_CCxCmd
    motor.o(i.set_motor_enable) refers to myconfig.o(.bss) for Flag
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart1.o(i.USART1_IRQHandler) refers to myconfig.o(.bss) for Flag
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.data) for TargerNum
    usart1.o(i.Usart1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.Usart1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.Usart1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.Usart2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart2.o(i.Usart2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.Usart2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.Usart2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart2.o(i.Usart2_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart2.o(i.Usart2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart3.o(i.Usart3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    tim1.o(i.TIM1_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim3.o(i.Tim3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim3.o(i.Tim3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim3.o(i.Tim3_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    tim3.o(i.Tim3_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim3.o(i.Tim3_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim3.o(i.Tim3_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Read_Pulse) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Read_Pulse) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.TIM2_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.TIM4_Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    pid.o(i.LocationRing_PID_Realize) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    pid.o(i.LocationRing_PID_Realize) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(i.LocationRing_PID_Realize) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.LocationRing_PID_Realize) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(i.LocationRing_PID_Realize) refers to pid.o(.bss) for PID
    pid.o(i.PID_Param_Init) refers to pid.o(.bss) for PID
    pid.o(i.VelocityRing_PID_Realize) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    pid.o(i.VelocityRing_PID_Realize) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    pid.o(i.VelocityRing_PID_Realize) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    pid.o(i.VelocityRing_PID_Realize) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(i.VelocityRing_PID_Realize) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.VelocityRing_PID_Realize) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(i.VelocityRing_PID_Realize) refers to pid.o(.bss) for PID
    niming.o(i.ANO_DT_Send_F1) refers to niming.o(i.USART2_SendArray) for USART2_SendArray
    niming.o(i.ANO_DT_Send_F1) refers to niming.o(.bss) for data_to_send
    niming.o(i.USART2_SendArray) refers to niming.o(i.USART2_SendByte) for USART2_SendByte
    niming.o(i.USART2_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    niming.o(i.USART2_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    huidu.o(i.Huidu_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    huidu.o(i.Huidu_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    huidu.o(i.Huidu_Receive1) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive2) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive3) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive4) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive5) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive6) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive7) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    huidu.o(i.Huidu_Receive8) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.main) refers to myconfig.o(i.NVIC_Config) for NVIC_Config
    main.o(i.main) refers to led.o(i.LED_GPIO_Init) for LED_GPIO_Init
    main.o(i.main) refers to usart1.o(i.Usart1_Init) for Usart1_Init
    main.o(i.main) refers to tim3.o(i.Tim3_Init) for Tim3_Init
    main.o(i.main) refers to tim1.o(i.TIM1_PWM_Init) for TIM1_PWM_Init
    main.o(i.main) refers to encoder.o(i.TIM2_Encoder_Init) for TIM2_Encoder_Init
    main.o(i.main) refers to encoder.o(i.TIM4_Encoder_Init) for TIM4_Encoder_Init
    main.o(i.main) refers to motor.o(i.Motor_GPIO_Init) for Motor_GPIO_Init
    main.o(i.main) refers to infrared.o(i.Infrafred_GPIO_Init) for Infrafred_GPIO_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to pid.o(i.PID_Param_Init) for PID_Param_Init
    main.o(i.main) refers to huidu.o(i.Huidu_GPIO_Init) for Huidu_GPIO_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    main.o(i.main) refers to infrared.o(i.LoadOrNot) for LoadOrNot
    main.o(i.main) refers to myconfig.o(i.Car_Tracking) for Car_Tracking
    main.o(i.main) refers to myconfig.o(i.Car_Spin) for Car_Spin
    main.o(i.main) refers to led.o(i.LED_RED_ON) for LED_RED_ON
    main.o(i.main) refers to myconfig.o(i.Digital_recognition) for Digital_recognition
    main.o(i.main) refers to myconfig.o(.bss) for Param
    main.o(i.main) refers to main.o(.data) for temp
    main.o(i.main) refers to usart1.o(.data) for TargerNum
    main.o(i.main) refers to myconfig.o(.data) for time
    main.o(i.main) refers to led.o(i.LED_RED_OFF) for LED_RED_OFF
    main.o(i.main) refers to led.o(i.LED_GREEN_ON) for LED_GREEN_ON
    myconfig.o(i.Car_Spin) refers to motor.o(i.Motor_Left_DIR) for Motor_Left_DIR
    myconfig.o(i.Car_Spin) refers to motor.o(i.Motor_Right_DIR) for Motor_Right_DIR
    myconfig.o(i.Car_Spin) refers to motor.o(i.set_motor_enable) for set_motor_enable
    myconfig.o(i.Car_Spin) refers to myconfig.o(.bss) for Flag
    myconfig.o(i.Car_Spin) refers to pid.o(.bss) for PID
    myconfig.o(i.Car_Tracking) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    myconfig.o(i.Car_Tracking) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    myconfig.o(i.Car_Tracking) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    myconfig.o(i.Car_Tracking) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    myconfig.o(i.Car_Tracking) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    myconfig.o(i.Car_Tracking) refers to motor.o(i.Motor_Left_DIR) for Motor_Left_DIR
    myconfig.o(i.Car_Tracking) refers to motor.o(i.Motor_Right_DIR) for Motor_Right_DIR
    myconfig.o(i.Car_Tracking) refers to motor.o(i.set_motor_enable) for set_motor_enable
    myconfig.o(i.Car_Tracking) refers to myconfig.o(.bss) for Flag
    myconfig.o(i.Car_Tracking) refers to pid.o(.bss) for PID
    myconfig.o(i.Digital_recognition) refers to motor.o(i.set_motor_disable) for set_motor_disable
    myconfig.o(i.Digital_recognition) refers to myconfig.o(.bss) for Flag
    myconfig.o(i.LocationRing_Out) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    myconfig.o(i.LocationRing_Out) refers to pid.o(i.LocationRing_PID_Realize) for LocationRing_PID_Realize
    myconfig.o(i.LocationRing_Out) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    myconfig.o(i.LocationRing_Out) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    myconfig.o(i.LocationRing_Out) refers to myconfig.o(.bss) for Param
    myconfig.o(i.LocationRing_Out) refers to pid.o(.bss) for PID
    myconfig.o(i.LocationRing_VelocityRing_Control) refers to myconfig.o(i.LocationRing_Out) for LocationRing_Out
    myconfig.o(i.LocationRing_VelocityRing_Control) refers to myconfig.o(i.VelocityRing_Out) for VelocityRing_Out
    myconfig.o(i.LocationRing_VelocityRing_Control) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    myconfig.o(i.LocationRing_VelocityRing_Control) refers to pid.o(.bss) for PID
    myconfig.o(i.NVIC_Config) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    myconfig.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    myconfig.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    myconfig.o(i.TIM3_IRQHandler) refers to encoder.o(i.Read_Pulse) for Read_Pulse
    myconfig.o(i.TIM3_IRQHandler) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    myconfig.o(i.TIM3_IRQHandler) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    myconfig.o(i.TIM3_IRQHandler) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive1) for Huidu_Receive1
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive2) for Huidu_Receive2
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive3) for Huidu_Receive3
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive4) for Huidu_Receive4
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive5) for Huidu_Receive5
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive6) for Huidu_Receive6
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive7) for Huidu_Receive7
    myconfig.o(i.TIM3_IRQHandler) refers to huidu.o(i.Huidu_Receive8) for Huidu_Receive8
    myconfig.o(i.TIM3_IRQHandler) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    myconfig.o(i.TIM3_IRQHandler) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    myconfig.o(i.TIM3_IRQHandler) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    myconfig.o(i.TIM3_IRQHandler) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    myconfig.o(i.TIM3_IRQHandler) refers to motor.o(i.set_motor_disable) for set_motor_disable
    myconfig.o(i.TIM3_IRQHandler) refers to myconfig.o(i.LocationRing_VelocityRing_Control) for LocationRing_VelocityRing_Control
    myconfig.o(i.TIM3_IRQHandler) refers to motor.o(i.Load_Motor_PWM) for Load_Motor_PWM
    myconfig.o(i.TIM3_IRQHandler) refers to myconfig.o(.bss) for Param
    myconfig.o(i.TIM3_IRQHandler) refers to myconfig.o(.data) for stop_time_cnt
    myconfig.o(i.TIM3_IRQHandler) refers to pid.o(.bss) for PID
    myconfig.o(i.VelocityRing_Out) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    myconfig.o(i.VelocityRing_Out) refers to pid.o(i.VelocityRing_PID_Realize) for VelocityRing_PID_Realize
    myconfig.o(i.VelocityRing_Out) refers to myconfig.o(.bss) for Param
    myconfig.o(i.VelocityRing_Out) refers to pid.o(.bss) for PID
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing led.o(i.LED_GREEN_OFF), (16 bytes).
    Removing led.o(i.LED_YELLOW_OFF), (20 bytes).
    Removing led.o(i.LED_YELLOW_ON), (20 bytes).
    Removing key.o(i.Key_GetNum), (28 bytes).
    Removing key.o(i.Key_Init), (44 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart2.o(i.Usart2_Init), (168 bytes).
    Removing usart3.o(i.Usart3_Init), (168 bytes).
    Removing niming.o(i.ANO_DT_Send_F1), (212 bytes).
    Removing niming.o(i.USART2_SendArray), (26 bytes).
    Removing niming.o(i.USART2_SendByte), (32 bytes).
    Removing niming.o(.bss), (100 bytes).

449 unused section(s) (total 17738 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    Hardware\Infrared.c                      0x00000000   Number         0  infrared.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\Motor.c                         0x00000000   Number         0  motor.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\PID.c                           0x00000000   Number         0  pid.o ABSOLUTE
    Hardware\\sys.c                          0x00000000   Number         0  sys.o ABSOLUTE
    Hardware\encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\huidu.c                         0x00000000   Number         0  huidu.o ABSOLUTE
    Hardware\niming.c                        0x00000000   Number         0  niming.o ABSOLUTE
    Hardware\sys.c                           0x00000000   Number         0  sys.o ABSOLUTE
    Hardware\tim1.c                          0x00000000   Number         0  tim1.o ABSOLUTE
    Hardware\tim3.c                          0x00000000   Number         0  tim3.o ABSOLUTE
    Hardware\usart1.c                        0x00000000   Number         0  usart1.o ABSOLUTE
    Hardware\usart2.c                        0x00000000   Number         0  usart2.o ABSOLUTE
    Hardware\usart3.c                        0x00000000   Number         0  usart3.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\MyConfig.c                          0x00000000   Number         0  myconfig.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section        0  heapauxi.o(.text)
    .text                                    0x080001ce   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000218   Section        0  exit.o(.text)
    .text                                    0x0800022c   Section        8  libspace.o(.text)
    .text                                    0x08000234   Section        0  sys_exit.o(.text)
    .text                                    0x08000240   Section        2  use_no_semi.o(.text)
    .text                                    0x08000242   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x08000242   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Car_Spin                               0x08000248   Section        0  myconfig.o(i.Car_Spin)
    i.Car_Tracking                           0x080002d8   Section        0  myconfig.o(i.Car_Tracking)
    i.DebugMon_Handler                       0x08000354   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08000356   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x0800036e   Section        0  delay.o(i.Delay_us)
    i.Digital_recognition                    0x0800039c   Section        0  myconfig.o(i.Digital_recognition)
    i.GPIO_Init                              0x080003b4   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x080004ca   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080004dc   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080004e0   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x080004e4   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x080004ee   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Huidu_GPIO_Init                        0x080004f4   Section        0  huidu.o(i.Huidu_GPIO_Init)
    i.Huidu_Receive1                         0x08000548   Section        0  huidu.o(i.Huidu_Receive1)
    i.Huidu_Receive2                         0x08000564   Section        0  huidu.o(i.Huidu_Receive2)
    i.Huidu_Receive3                         0x0800057c   Section        0  huidu.o(i.Huidu_Receive3)
    i.Huidu_Receive4                         0x08000594   Section        0  huidu.o(i.Huidu_Receive4)
    i.Huidu_Receive5                         0x080005ac   Section        0  huidu.o(i.Huidu_Receive5)
    i.Huidu_Receive6                         0x080005c4   Section        0  huidu.o(i.Huidu_Receive6)
    i.Huidu_Receive7                         0x080005dc   Section        0  huidu.o(i.Huidu_Receive7)
    i.Huidu_Receive8                         0x080005f4   Section        0  huidu.o(i.Huidu_Receive8)
    i.Infrafred_GPIO_Init                    0x0800060c   Section        0  infrared.o(i.Infrafred_GPIO_Init)
    i.LED_GPIO_Init                          0x08000638   Section        0  led.o(i.LED_GPIO_Init)
    i.LED_GREEN_ON                           0x080006a4   Section        0  led.o(i.LED_GREEN_ON)
    i.LED_RED_OFF                            0x080006b4   Section        0  led.o(i.LED_RED_OFF)
    i.LED_RED_ON                             0x080006c4   Section        0  led.o(i.LED_RED_ON)
    i.LoadOrNot                              0x080006d4   Section        0  infrared.o(i.LoadOrNot)
    i.Load_Motor_PWM                         0x08000720   Section        0  motor.o(i.Load_Motor_PWM)
    i.LocationRing_Out                       0x08000770   Section        0  myconfig.o(i.LocationRing_Out)
    i.LocationRing_PID_Realize               0x080007bc   Section        0  pid.o(i.LocationRing_PID_Realize)
    i.LocationRing_VelocityRing_Control      0x08000834   Section        0  myconfig.o(i.LocationRing_VelocityRing_Control)
    i.MemManage_Handler                      0x08000854   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_GPIO_Init                        0x08000858   Section        0  motor.o(i.Motor_GPIO_Init)
    i.Motor_Left_DIR                         0x08000884   Section        0  motor.o(i.Motor_Left_DIR)
    i.Motor_Right_DIR                        0x080008c4   Section        0  motor.o(i.Motor_Right_DIR)
    i.NMI_Handler                            0x08000904   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Config                            0x08000906   Section        0  myconfig.o(i.NVIC_Config)
    i.NVIC_Init                              0x08000914   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000984   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08000998   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080009c4   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08000a14   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08000a68   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08000a9c   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08000ac4   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08000b72   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08000b86   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08000ba8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowNum                           0x08000c1c   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_ShowString                        0x08000c60   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08000c88   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000ca8   Section        0  oled.o(i.OLED_WriteData)
    i.PID_Param_Init                         0x08000cc8   Section        0  pid.o(i.PID_Param_Init)
    i.PendSV_Handler                         0x08000d14   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000d18   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000d38   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000d58   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Read_Pulse                             0x08000e2c   Section        0  encoder.o(i.Read_Pulse)
    i.SVC_Handler                            0x08000e70   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08000e72   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08000e73   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000e7c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000e7d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000f5c   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000f60   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TI1_Config                             0x08000fc0   Section        0  stm32f10x_tim.o(i.TI1_Config)
    TI1_Config                               0x08000fc1   Thumb Code   108  stm32f10x_tim.o(i.TI1_Config)
    i.TI2_Config                             0x08001040   Section        0  stm32f10x_tim.o(i.TI2_Config)
    TI2_Config                               0x08001041   Thumb Code   130  stm32f10x_tim.o(i.TI2_Config)
    i.TI3_Config                             0x080010d8   Section        0  stm32f10x_tim.o(i.TI3_Config)
    TI3_Config                               0x080010d9   Thumb Code   122  stm32f10x_tim.o(i.TI3_Config)
    i.TI4_Config                             0x08001168   Section        0  stm32f10x_tim.o(i.TI4_Config)
    TI4_Config                               0x08001169   Thumb Code   130  stm32f10x_tim.o(i.TI4_Config)
    i.TIM1_PWM_Init                          0x08001200   Section        0  tim1.o(i.TIM1_PWM_Init)
    i.TIM2_Encoder_Init                      0x080012b0   Section        0  encoder.o(i.TIM2_Encoder_Init)
    i.TIM2_IRQHandler                        0x08001344   Section        0  encoder.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x0800135c   Section        0  myconfig.o(i.TIM3_IRQHandler)
    i.TIM4_Encoder_Init                      0x080016fc   Section        0  encoder.o(i.TIM4_Encoder_Init)
    i.TIM4_IRQHandler                        0x08001790   Section        0  encoder.o(i.TIM4_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x080017ac   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_CCxCmd                             0x080017c4   Section        0  stm32f10x_tim.o(i.TIM_CCxCmd)
    i.TIM_ClearITPendingBit                  0x080017e2   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080017e8   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08001800   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_EncoderInterfaceConfig             0x0800181e   Section        0  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    i.TIM_GetCounter                         0x08001860   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x08001866   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ICInit                             0x08001888   Section        0  stm32f10x_tim.o(i.TIM_ICInit)
    i.TIM_ITConfig                           0x08001934   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC1Init                            0x08001948   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x080019e0   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC4Init                            0x080019f4   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x08001a70   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_OCStructInit                       0x08001a8a   Section        0  stm32f10x_tim.o(i.TIM_OCStructInit)
    i.TIM_SetCompare1                        0x08001a9e   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_SetCompare4                        0x08001aa2   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_SetCounter                         0x08001aa8   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_SetIC1Prescaler                    0x08001aac   Section        0  stm32f10x_tim.o(i.TIM_SetIC1Prescaler)
    i.TIM_SetIC2Prescaler                    0x08001abe   Section        0  stm32f10x_tim.o(i.TIM_SetIC2Prescaler)
    i.TIM_SetIC3Prescaler                    0x08001ad8   Section        0  stm32f10x_tim.o(i.TIM_SetIC3Prescaler)
    i.TIM_SetIC4Prescaler                    0x08001aea   Section        0  stm32f10x_tim.o(i.TIM_SetIC4Prescaler)
    i.TIM_TimeBaseInit                       0x08001b04   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.TIM_TimeBaseStructInit                 0x08001ba8   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseStructInit)
    i.Tim3_Init                              0x08001bbc   Section        0  tim3.o(i.Tim3_Init)
    i.USART1_IRQHandler                      0x08001c1c   Section        0  usart1.o(i.USART1_IRQHandler)
    i.USART_ClearITPendingBit                0x08001c5c   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001c7a   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001c92   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001ce6   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001d30   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001e08   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001e12   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08001e1a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.Usart1_Init                            0x08001e20   Section        0  usart1.o(i.Usart1_Init)
    i.VelocityRing_Out                       0x08001ec8   Section        0  myconfig.o(i.VelocityRing_Out)
    i.VelocityRing_PID_Realize               0x08001f04   Section        0  pid.o(i.VelocityRing_PID_Realize)
    i.main                                   0x08001fd0   Section        0  main.o(i.main)
    i.set_motor_disable                      0x08002e38   Section        0  motor.o(i.set_motor_disable)
    i.set_motor_enable                       0x08002e60   Section        0  motor.o(i.set_motor_enable)
    x$fpl$d2f                                0x08002e88   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x08002eec   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08002ef3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dflt                               0x0800319c   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080031ca   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x080031f0   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08003344   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080033e0   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$fadd                               0x080033ec   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080033fb   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x080034b0   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$ffix                               0x080034c8   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08003500   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08003540   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08003570   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x08003598   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08003600   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08003702   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x0800378e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08003798   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$frsb                               0x080037fa   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08003810   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x0800381f   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    .constdata                               0x080038fa   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x080038fa   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        1  infrared.o(.data)
    load_flag                                0x20000014   Data           1  infrared.o(.data)
    .data                                    0x20000015   Section        1  usart1.o(.data)
    .data                                    0x20000018   Section        8  main.o(.data)
    .data                                    0x20000020   Section       16  myconfig.o(.data)
    .bss                                     0x20000030   Section       72  pid.o(.bss)
    .bss                                     0x20000078   Section       46  myconfig.o(.bss)
    .bss                                     0x200000a8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000108   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x20000108   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x20000308   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x20000308   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000708   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_two_region_memory                  0x080001c9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001cb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001cd   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080001cf   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000219   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x0800022d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800022d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800022d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000235   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000241   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000241   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x08000243   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x08000243   Thumb Code     0  indicate_semi.o(.text)
    Car_Spin                                 0x08000249   Thumb Code   122  myconfig.o(i.Car_Spin)
    Car_Tracking                             0x080002d9   Thumb Code    98  myconfig.o(i.Car_Tracking)
    DebugMon_Handler                         0x08000355   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08000357   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x0800036f   Thumb Code    46  delay.o(i.Delay_us)
    Digital_recognition                      0x0800039d   Thumb Code    18  myconfig.o(i.Digital_recognition)
    GPIO_Init                                0x080003b5   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x080004cb   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080004dd   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080004e1   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x080004e5   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x080004ef   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Huidu_GPIO_Init                          0x080004f5   Thumb Code    74  huidu.o(i.Huidu_GPIO_Init)
    Huidu_Receive1                           0x08000549   Thumb Code    22  huidu.o(i.Huidu_Receive1)
    Huidu_Receive2                           0x08000565   Thumb Code    20  huidu.o(i.Huidu_Receive2)
    Huidu_Receive3                           0x0800057d   Thumb Code    20  huidu.o(i.Huidu_Receive3)
    Huidu_Receive4                           0x08000595   Thumb Code    20  huidu.o(i.Huidu_Receive4)
    Huidu_Receive5                           0x080005ad   Thumb Code    20  huidu.o(i.Huidu_Receive5)
    Huidu_Receive6                           0x080005c5   Thumb Code    20  huidu.o(i.Huidu_Receive6)
    Huidu_Receive7                           0x080005dd   Thumb Code    20  huidu.o(i.Huidu_Receive7)
    Huidu_Receive8                           0x080005f5   Thumb Code    20  huidu.o(i.Huidu_Receive8)
    Infrafred_GPIO_Init                      0x0800060d   Thumb Code    40  infrared.o(i.Infrafred_GPIO_Init)
    LED_GPIO_Init                            0x08000639   Thumb Code   100  led.o(i.LED_GPIO_Init)
    LED_GREEN_ON                             0x080006a5   Thumb Code    12  led.o(i.LED_GREEN_ON)
    LED_RED_OFF                              0x080006b5   Thumb Code    12  led.o(i.LED_RED_OFF)
    LED_RED_ON                               0x080006c5   Thumb Code    12  led.o(i.LED_RED_ON)
    LoadOrNot                                0x080006d5   Thumb Code    66  infrared.o(i.LoadOrNot)
    Load_Motor_PWM                           0x08000721   Thumb Code    76  motor.o(i.Load_Motor_PWM)
    LocationRing_Out                         0x08000771   Thumb Code    60  myconfig.o(i.LocationRing_Out)
    LocationRing_PID_Realize                 0x080007bd   Thumb Code   114  pid.o(i.LocationRing_PID_Realize)
    LocationRing_VelocityRing_Control        0x08000835   Thumb Code    26  myconfig.o(i.LocationRing_VelocityRing_Control)
    MemManage_Handler                        0x08000855   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    Motor_GPIO_Init                          0x08000859   Thumb Code    40  motor.o(i.Motor_GPIO_Init)
    Motor_Left_DIR                           0x08000885   Thumb Code    54  motor.o(i.Motor_Left_DIR)
    Motor_Right_DIR                          0x080008c5   Thumb Code    56  motor.o(i.Motor_Right_DIR)
    NMI_Handler                              0x08000905   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Config                              0x08000907   Thumb Code    12  myconfig.o(i.NVIC_Config)
    NVIC_Init                                0x08000915   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000985   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08000999   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080009c5   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08000a15   Thumb Code    80  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08000a69   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08000a9d   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08000ac5   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08000b73   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x08000b87   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08000ba9   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowNum                             0x08000c1d   Thumb Code    68  oled.o(i.OLED_ShowNum)
    OLED_ShowString                          0x08000c61   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08000c89   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000ca9   Thumb Code    32  oled.o(i.OLED_WriteData)
    PID_Param_Init                           0x08000cc9   Thumb Code    66  pid.o(i.PID_Param_Init)
    PendSV_Handler                           0x08000d15   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000d19   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000d39   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000d59   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Read_Pulse                               0x08000e2d   Thumb Code    62  encoder.o(i.Read_Pulse)
    SVC_Handler                              0x08000e71   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000f5d   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08000f61   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM1_PWM_Init                            0x08001201   Thumb Code   166  tim1.o(i.TIM1_PWM_Init)
    TIM2_Encoder_Init                        0x080012b1   Thumb Code   144  encoder.o(i.TIM2_Encoder_Init)
    TIM2_IRQHandler                          0x08001345   Thumb Code    24  encoder.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x0800135d   Thumb Code   878  myconfig.o(i.TIM3_IRQHandler)
    TIM4_Encoder_Init                        0x080016fd   Thumb Code   138  encoder.o(i.TIM4_Encoder_Init)
    TIM4_IRQHandler                          0x08001791   Thumb Code    24  encoder.o(i.TIM4_IRQHandler)
    TIM_ARRPreloadConfig                     0x080017ad   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_CCxCmd                               0x080017c5   Thumb Code    30  stm32f10x_tim.o(i.TIM_CCxCmd)
    TIM_ClearITPendingBit                    0x080017e3   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080017e9   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08001801   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_EncoderInterfaceConfig               0x0800181f   Thumb Code    66  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    TIM_GetCounter                           0x08001861   Thumb Code     6  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x08001867   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ICInit                               0x08001889   Thumb Code   150  stm32f10x_tim.o(i.TIM_ICInit)
    TIM_ITConfig                             0x08001935   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC1Init                              0x08001949   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080019e1   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC4Init                              0x080019f5   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08001a71   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_OCStructInit                         0x08001a8b   Thumb Code    20  stm32f10x_tim.o(i.TIM_OCStructInit)
    TIM_SetCompare1                          0x08001a9f   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_SetCompare4                          0x08001aa3   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_SetCounter                           0x08001aa9   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_SetIC1Prescaler                      0x08001aad   Thumb Code    18  stm32f10x_tim.o(i.TIM_SetIC1Prescaler)
    TIM_SetIC2Prescaler                      0x08001abf   Thumb Code    26  stm32f10x_tim.o(i.TIM_SetIC2Prescaler)
    TIM_SetIC3Prescaler                      0x08001ad9   Thumb Code    18  stm32f10x_tim.o(i.TIM_SetIC3Prescaler)
    TIM_SetIC4Prescaler                      0x08001aeb   Thumb Code    26  stm32f10x_tim.o(i.TIM_SetIC4Prescaler)
    TIM_TimeBaseInit                         0x08001b05   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    TIM_TimeBaseStructInit                   0x08001ba9   Thumb Code    18  stm32f10x_tim.o(i.TIM_TimeBaseStructInit)
    Tim3_Init                                0x08001bbd   Thumb Code    92  tim3.o(i.Tim3_Init)
    USART1_IRQHandler                        0x08001c1d   Thumb Code    52  usart1.o(i.USART1_IRQHandler)
    USART_ClearITPendingBit                  0x08001c5d   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001c7b   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001c93   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001ce7   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001d31   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001e09   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001e13   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08001e1b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    Usart1_Init                              0x08001e21   Thumb Code   158  usart1.o(i.Usart1_Init)
    VelocityRing_Out                         0x08001ec9   Thumb Code    52  myconfig.o(i.VelocityRing_Out)
    VelocityRing_PID_Realize                 0x08001f05   Thumb Code   192  pid.o(i.VelocityRing_PID_Realize)
    main                                     0x08001fd1   Thumb Code  3662  main.o(i.main)
    set_motor_disable                        0x08002e39   Thumb Code    30  motor.o(i.set_motor_disable)
    set_motor_enable                         0x08002e61   Thumb Code    30  motor.o(i.set_motor_enable)
    __aeabi_d2f                              0x08002e89   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08002e89   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x08002eed   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002eed   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_i2d                              0x0800319d   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800319d   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080031cb   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080031cb   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x080031f1   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080031f1   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08003345   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080033e1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_fadd                             0x080033ed   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080033ed   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x080034b1   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_f2iz                             0x080034c9   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x080034c9   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08003501   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08003501   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08003541   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08003541   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08003571   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08003571   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x08003599   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08003599   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x080035eb   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08003601   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08003601   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08003703   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x0800378f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08003799   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08003799   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_frsub                            0x080037fb   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x080037fb   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08003811   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08003811   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    OLED_F8x16                               0x080038fa   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x080038fa   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08003eec   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003f0c   Number         0  anon$$obj.o(Region$$Table)
    TargerNum                                0x20000015   Data           1  usart1.o(.data)
    RoadLineCheck                            0x20000018   Data           2  main.o(.data)
    RoadLine                                 0x2000001a   Data           1  main.o(.data)
    temp                                     0x2000001c   Data           4  main.o(.data)
    stop_time_cnt                            0x20000020   Data           2  myconfig.o(.data)
    spin_time_cnt                            0x20000022   Data           2  myconfig.o(.data)
    Calibration                              0x20000024   Data           4  myconfig.o(.data)
    time                                     0x20000028   Data           2  myconfig.o(.data)
    GetNum                                   0x2000002a   Data           1  myconfig.o(.data)
    Spin_time                                0x2000002c   Data           2  myconfig.o(.data)
    Spin180_Time                             0x2000002e   Data           2  myconfig.o(.data)
    PID                                      0x20000030   Data          72  pid.o(.bss)
    Param                                    0x20000078   Data          32  myconfig.o(.bss)
    Flag                                     0x20000098   Data          14  myconfig.o(.bss)
    __libspace_start                         0x200000a8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000108   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003f3c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003f0c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         3950  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4158    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4160    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4162    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         4028    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4035    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4037    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4040    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4042    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4044    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4047    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4049    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4051    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4053    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4055    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4057    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4059    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4061    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4063    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4065    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4067    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4071    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4073    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4075    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4077    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         4078    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         4098    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4111    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4113    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4116    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4119    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4121    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4124    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         4125    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         3992    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         4005    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         4017    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         4007    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         4008    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4010    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         4011    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         4032    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         4080    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         4081    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         4082    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001c8   0x080001c8   0x00000006   Code   RO         3948    .text               c_w.l(heapauxi.o)
    0x080001ce   0x080001ce   0x0000004a   Code   RO         4019    .text               c_w.l(sys_stackheap_outer.o)
    0x08000218   0x08000218   0x00000012   Code   RO         4021    .text               c_w.l(exit.o)
    0x0800022a   0x0800022a   0x00000002   PAD
    0x0800022c   0x0800022c   0x00000008   Code   RO         4029    .text               c_w.l(libspace.o)
    0x08000234   0x08000234   0x0000000c   Code   RO         4090    .text               c_w.l(sys_exit.o)
    0x08000240   0x08000240   0x00000002   Code   RO         4101    .text               c_w.l(use_no_semi.o)
    0x08000242   0x08000242   0x00000000   Code   RO         4103    .text               c_w.l(indicate_semi.o)
    0x08000242   0x08000242   0x00000004   Code   RO         3822    i.BusFault_Handler  stm32f10x_it.o
    0x08000246   0x08000246   0x00000002   PAD
    0x08000248   0x08000248   0x00000090   Code   RO         3885    i.Car_Spin          myconfig.o
    0x080002d8   0x080002d8   0x0000007c   Code   RO         3886    i.Car_Tracking      myconfig.o
    0x08000354   0x08000354   0x00000002   Code   RO         3823    i.DebugMon_Handler  stm32f10x_it.o
    0x08000356   0x08000356   0x00000018   Code   RO         3196    i.Delay_ms          delay.o
    0x0800036e   0x0800036e   0x0000002e   Code   RO         3198    i.Delay_us          delay.o
    0x0800039c   0x0800039c   0x00000018   Code   RO         3887    i.Digital_recognition  myconfig.o
    0x080003b4   0x080003b4   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x080004ca   0x080004ca   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080004dc   0x080004dc   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080004e0   0x080004e0   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080004e4   0x080004e4   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x080004ee   0x080004ee   0x00000004   Code   RO         3824    i.HardFault_Handler  stm32f10x_it.o
    0x080004f2   0x080004f2   0x00000002   PAD
    0x080004f4   0x080004f4   0x00000054   Code   RO         3740    i.Huidu_GPIO_Init   huidu.o
    0x08000548   0x08000548   0x0000001c   Code   RO         3741    i.Huidu_Receive1    huidu.o
    0x08000564   0x08000564   0x00000018   Code   RO         3742    i.Huidu_Receive2    huidu.o
    0x0800057c   0x0800057c   0x00000018   Code   RO         3743    i.Huidu_Receive3    huidu.o
    0x08000594   0x08000594   0x00000018   Code   RO         3744    i.Huidu_Receive4    huidu.o
    0x080005ac   0x080005ac   0x00000018   Code   RO         3745    i.Huidu_Receive5    huidu.o
    0x080005c4   0x080005c4   0x00000018   Code   RO         3746    i.Huidu_Receive6    huidu.o
    0x080005dc   0x080005dc   0x00000018   Code   RO         3747    i.Huidu_Receive7    huidu.o
    0x080005f4   0x080005f4   0x00000018   Code   RO         3748    i.Huidu_Receive8    huidu.o
    0x0800060c   0x0800060c   0x0000002c   Code   RO         3483    i.Infrafred_GPIO_Init  infrared.o
    0x08000638   0x08000638   0x0000006c   Code   RO         3220    i.LED_GPIO_Init     led.o
    0x080006a4   0x080006a4   0x00000010   Code   RO         3222    i.LED_GREEN_ON      led.o
    0x080006b4   0x080006b4   0x00000010   Code   RO         3223    i.LED_RED_OFF       led.o
    0x080006c4   0x080006c4   0x00000010   Code   RO         3224    i.LED_RED_ON        led.o
    0x080006d4   0x080006d4   0x0000004c   Code   RO         3484    i.LoadOrNot         infrared.o
    0x08000720   0x08000720   0x00000050   Code   RO         3505    i.Load_Motor_PWM    motor.o
    0x08000770   0x08000770   0x0000004c   Code   RO         3888    i.LocationRing_Out  myconfig.o
    0x080007bc   0x080007bc   0x00000078   Code   RO         3673    i.LocationRing_PID_Realize  pid.o
    0x08000834   0x08000834   0x00000020   Code   RO         3889    i.LocationRing_VelocityRing_Control  myconfig.o
    0x08000854   0x08000854   0x00000004   Code   RO         3825    i.MemManage_Handler  stm32f10x_it.o
    0x08000858   0x08000858   0x0000002c   Code   RO         3506    i.Motor_GPIO_Init   motor.o
    0x08000884   0x08000884   0x00000040   Code   RO         3507    i.Motor_Left_DIR    motor.o
    0x080008c4   0x080008c4   0x00000040   Code   RO         3508    i.Motor_Right_DIR   motor.o
    0x08000904   0x08000904   0x00000002   Code   RO         3826    i.NMI_Handler       stm32f10x_it.o
    0x08000906   0x08000906   0x0000000c   Code   RO         3890    i.NVIC_Config       myconfig.o
    0x08000912   0x08000912   0x00000002   PAD
    0x08000914   0x08000914   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08000984   0x08000984   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x08000998   0x08000998   0x0000002a   Code   RO         3343    i.OLED_Clear        oled.o
    0x080009c2   0x080009c2   0x00000002   PAD
    0x080009c4   0x080009c4   0x00000050   Code   RO         3344    i.OLED_I2C_Init     oled.o
    0x08000a14   0x08000a14   0x00000054   Code   RO         3345    i.OLED_I2C_SendByte  oled.o
    0x08000a68   0x08000a68   0x00000034   Code   RO         3346    i.OLED_I2C_Start    oled.o
    0x08000a9c   0x08000a9c   0x00000028   Code   RO         3347    i.OLED_I2C_Stop     oled.o
    0x08000ac4   0x08000ac4   0x000000ae   Code   RO         3348    i.OLED_Init         oled.o
    0x08000b72   0x08000b72   0x00000014   Code   RO         3349    i.OLED_Pow          oled.o
    0x08000b86   0x08000b86   0x00000022   Code   RO         3350    i.OLED_SetCursor    oled.o
    0x08000ba8   0x08000ba8   0x00000074   Code   RO         3352    i.OLED_ShowChar     oled.o
    0x08000c1c   0x08000c1c   0x00000044   Code   RO         3354    i.OLED_ShowNum      oled.o
    0x08000c60   0x08000c60   0x00000028   Code   RO         3356    i.OLED_ShowString   oled.o
    0x08000c88   0x08000c88   0x00000020   Code   RO         3357    i.OLED_WriteCommand  oled.o
    0x08000ca8   0x08000ca8   0x00000020   Code   RO         3358    i.OLED_WriteData    oled.o
    0x08000cc8   0x08000cc8   0x0000004c   Code   RO         3674    i.PID_Param_Init    pid.o
    0x08000d14   0x08000d14   0x00000002   Code   RO         3827    i.PendSV_Handler    stm32f10x_it.o
    0x08000d16   0x08000d16   0x00000002   PAD
    0x08000d18   0x08000d18   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000d38   0x08000d38   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000d58   0x08000d58   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000e2c   0x08000e2c   0x00000044   Code   RO         3634    i.Read_Pulse        encoder.o
    0x08000e70   0x08000e70   0x00000002   Code   RO         3828    i.SVC_Handler       stm32f10x_it.o
    0x08000e72   0x08000e72   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x08000e7a   0x08000e7a   0x00000002   PAD
    0x08000e7c   0x08000e7c   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x08000f5c   0x08000f5c   0x00000002   Code   RO         3829    i.SysTick_Handler   stm32f10x_it.o
    0x08000f5e   0x08000f5e   0x00000002   PAD
    0x08000f60   0x08000f60   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x08000fc0   0x08000fc0   0x00000080   Code   RO         2405    i.TI1_Config        stm32f10x_tim.o
    0x08001040   0x08001040   0x00000098   Code   RO         2406    i.TI2_Config        stm32f10x_tim.o
    0x080010d8   0x080010d8   0x00000090   Code   RO         2407    i.TI3_Config        stm32f10x_tim.o
    0x08001168   0x08001168   0x00000098   Code   RO         2408    i.TI4_Config        stm32f10x_tim.o
    0x08001200   0x08001200   0x000000b0   Code   RO         3604    i.TIM1_PWM_Init     tim1.o
    0x080012b0   0x080012b0   0x00000094   Code   RO         3635    i.TIM2_Encoder_Init  encoder.o
    0x08001344   0x08001344   0x00000018   Code   RO         3636    i.TIM2_IRQHandler   encoder.o
    0x0800135c   0x0800135c   0x000003a0   Code   RO         3891    i.TIM3_IRQHandler   myconfig.o
    0x080016fc   0x080016fc   0x00000094   Code   RO         3637    i.TIM4_Encoder_Init  encoder.o
    0x08001790   0x08001790   0x0000001c   Code   RO         3638    i.TIM4_IRQHandler   encoder.o
    0x080017ac   0x080017ac   0x00000018   Code   RO         2409    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x080017c4   0x080017c4   0x0000001e   Code   RO         2413    i.TIM_CCxCmd        stm32f10x_tim.o
    0x080017e2   0x080017e2   0x00000006   Code   RO         2416    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x080017e8   0x080017e8   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x08001800   0x08001800   0x0000001e   Code   RO         2423    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x0800181e   0x0800181e   0x00000042   Code   RO         2430    i.TIM_EncoderInterfaceConfig  stm32f10x_tim.o
    0x08001860   0x08001860   0x00000006   Code   RO         2440    i.TIM_GetCounter    stm32f10x_tim.o
    0x08001866   0x08001866   0x00000022   Code   RO         2442    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001888   0x08001888   0x000000ac   Code   RO         2444    i.TIM_ICInit        stm32f10x_tim.o
    0x08001934   0x08001934   0x00000012   Code   RO         2446    i.TIM_ITConfig      stm32f10x_tim.o
    0x08001946   0x08001946   0x00000002   PAD
    0x08001948   0x08001948   0x00000098   Code   RO         2450    i.TIM_OC1Init       stm32f10x_tim.o
    0x080019e0   0x080019e0   0x00000012   Code   RO         2453    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x080019f2   0x080019f2   0x00000002   PAD
    0x080019f4   0x080019f4   0x0000007c   Code   RO         2465    i.TIM_OC4Init       stm32f10x_tim.o
    0x08001a70   0x08001a70   0x0000001a   Code   RO         2467    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08001a8a   0x08001a8a   0x00000014   Code   RO         2468    i.TIM_OCStructInit  stm32f10x_tim.o
    0x08001a9e   0x08001a9e   0x00000004   Code   RO         2482    i.TIM_SetCompare1   stm32f10x_tim.o
    0x08001aa2   0x08001aa2   0x00000006   Code   RO         2485    i.TIM_SetCompare4   stm32f10x_tim.o
    0x08001aa8   0x08001aa8   0x00000004   Code   RO         2486    i.TIM_SetCounter    stm32f10x_tim.o
    0x08001aac   0x08001aac   0x00000012   Code   RO         2487    i.TIM_SetIC1Prescaler  stm32f10x_tim.o
    0x08001abe   0x08001abe   0x0000001a   Code   RO         2488    i.TIM_SetIC2Prescaler  stm32f10x_tim.o
    0x08001ad8   0x08001ad8   0x00000012   Code   RO         2489    i.TIM_SetIC3Prescaler  stm32f10x_tim.o
    0x08001aea   0x08001aea   0x0000001a   Code   RO         2490    i.TIM_SetIC4Prescaler  stm32f10x_tim.o
    0x08001b04   0x08001b04   0x000000a4   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001ba8   0x08001ba8   0x00000012   Code   RO         2493    i.TIM_TimeBaseStructInit  stm32f10x_tim.o
    0x08001bba   0x08001bba   0x00000002   PAD
    0x08001bbc   0x08001bbc   0x00000060   Code   RO         3619    i.Tim3_Init         tim3.o
    0x08001c1c   0x08001c1c   0x00000040   Code   RO         3550    i.USART1_IRQHandler  usart1.o
    0x08001c5c   0x08001c5c   0x0000001e   Code   RO         2957    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001c7a   0x08001c7a   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x08001c92   0x08001c92   0x00000054   Code   RO         2964    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001ce6   0x08001ce6   0x0000004a   Code   RO         2966    i.USART_ITConfig    stm32f10x_usart.o
    0x08001d30   0x08001d30   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08001e08   0x08001e08   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001e12   0x08001e12   0x00000008   Code   RO         2977    i.USART_SendData    stm32f10x_usart.o
    0x08001e1a   0x08001e1a   0x00000004   Code   RO         3830    i.UsageFault_Handler  stm32f10x_it.o
    0x08001e1e   0x08001e1e   0x00000002   PAD
    0x08001e20   0x08001e20   0x000000a8   Code   RO         3551    i.Usart1_Init       usart1.o
    0x08001ec8   0x08001ec8   0x0000003c   Code   RO         3892    i.VelocityRing_Out  myconfig.o
    0x08001f04   0x08001f04   0x000000cc   Code   RO         3675    i.VelocityRing_PID_Realize  pid.o
    0x08001fd0   0x08001fd0   0x00000e68   Code   RO         3803    i.main              main.o
    0x08002e38   0x08002e38   0x00000028   Code   RO         3509    i.set_motor_disable  motor.o
    0x08002e60   0x08002e60   0x00000028   Code   RO         3510    i.set_motor_enable  motor.o
    0x08002e88   0x08002e88   0x00000062   Code   RO         3952    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08002eea   0x08002eea   0x00000002   PAD
    0x08002eec   0x08002eec   0x000002b0   Code   RO         3955    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x0800319c   0x0800319c   0x0000002e   Code   RO         3959    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x080031ca   0x080031ca   0x00000026   Code   RO         3958    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080031f0   0x080031f0   0x00000154   Code   RO         3964    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08003344   0x08003344   0x0000009c   Code   RO         3993    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080033e0   0x080033e0   0x0000000c   Code   RO         3995    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080033ec   0x080033ec   0x000000c4   Code   RO         3966    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080034b0   0x080034b0   0x00000018   Code   RO         3997    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080034c8   0x080034c8   0x00000036   Code   RO         3972    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080034fe   0x080034fe   0x00000002   PAD
    0x08003500   0x08003500   0x0000003e   Code   RO         3976    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x0800353e   0x0800353e   0x00000002   PAD
    0x08003540   0x08003540   0x00000030   Code   RO         3981    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08003570   0x08003570   0x00000026   Code   RO         3980    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08003596   0x08003596   0x00000002   PAD
    0x08003598   0x08003598   0x00000068   Code   RO         3986    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08003600   0x08003600   0x00000102   Code   RO         3988    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08003702   0x08003702   0x0000008c   Code   RO         3999    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x0800378e   0x0800378e   0x0000000a   Code   RO         4001    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08003798   0x08003798   0x00000062   Code   RO         3990    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x080037fa   0x080037fa   0x00000014   Code   RO         3967    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x0800380e   0x0800380e   0x00000002   PAD
    0x08003810   0x08003810   0x000000ea   Code   RO         3968    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x080038fa   0x080038fa   0x00000000   Code   RO         4003    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080038fa   0x080038fa   0x000005f0   Data   RO         3359    .constdata          oled.o
    0x08003eea   0x08003eea   0x00000002   PAD
    0x08003eec   0x08003eec   0x00000020   Data   RO         4156    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003f0c, Size: 0x00000708, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003f0c   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000014   0x08003f20   0x00000001   Data   RW         3485    .data               infrared.o
    0x20000015   0x08003f21   0x00000001   Data   RW         3552    .data               usart1.o
    0x20000016   0x08003f22   0x00000002   PAD
    0x20000018   0x08003f24   0x00000008   Data   RW         3804    .data               main.o
    0x20000020   0x08003f2c   0x00000010   Data   RW         3894    .data               myconfig.o
    0x20000030        -       0x00000048   Zero   RW         3676    .bss                pid.o
    0x20000078        -       0x0000002e   Zero   RW         3893    .bss                myconfig.o
    0x200000a6   0x08003f3c   0x00000002   PAD
    0x200000a8        -       0x00000060   Zero   RW         4030    .bss                c_w.l(libspace.o)
    0x20000108        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x20000308        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0       4500   core_cm3.o
        70          0          0          0          0        994   delay.o
       416         24          0          0          0       2905   encoder.o
       280         44          0          0          0       4762   huidu.o
       120         14          0          1          0       1102   infrared.o
       156         20          0          0          0       1914   led.o
      3688        220          0          8          0       3693   main.o
       132         22          0          0          0     204919   misc.o
       332         46          0          0          0       3677   motor.o
      1400        134          0         16         46       6734   myconfig.o
       814         22       1520          0          0       8061   oled.o
       400         28          0          0         72       2729   pid.o
        64         26        236          0       1536        848   startup_stm32f10x_md.o
         0          0          0          0          0       1692   stm32f10x_adc.o
       314          0          0          0          0      12572   stm32f10x_gpio.o
        26          0          0          0          0       4046   stm32f10x_it.o
       276         32          0         20          0      13166   stm32f10x_rcc.o
      1610        180          0          0          0      38173   stm32f10x_tim.o
       446          6          0          0          0      12189   stm32f10x_usart.o
       328         28          0          0          0      45489   system_stm32f10x.o
       176         10          0          0          0        768   tim1.o
        96          4          0          0          0        568   tim3.o
       232         22          0          1          0       1474   usart1.o

    ----------------------------------------------------------------------
     11398        <USER>       <GROUP>         48       1656     376975   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          2          2          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       688        140          0          0          0        208   ddiv.o
        84          0          0          0          0        136   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       450          8          0          0          0        236   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      2952        <USER>          <GROUP>          0         96       2280   Library Totals
        16          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       272         16          0          0         96        584   c_w.l
      2664        188          0          0          0       1696   fz_ws.l

    ----------------------------------------------------------------------
      2952        <USER>          <GROUP>          0         96       2280   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14350       1086       1790         48       1752     372335   Grand Totals
     14350       1086       1790         48       1752     372335   ELF Image Totals
     14350       1086       1790         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16140 (  15.76kB)
    Total RW  Size (RW Data + ZI Data)              1800 (   1.76kB)
    Total ROM Size (Code + RO Data + RW Data)      16188 (  15.81kB)

==============================================================================

