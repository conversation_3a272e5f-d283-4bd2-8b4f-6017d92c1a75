#include "Key_Led.h"

/* 变量初始化 */
uint8_t Task_Flag = 0;         //主任务标志位  0-OFF 1-ON
uint8_t Task_State = 0;        //任务状态标志位数组变量 0-NULL 1-Task_1 2-Task_2 3_Task_3

/**
 * @brief 按键获取（未消抖）
 * 
 * @return uint8_t 
 */
uint8_t Key_Read(void)
{
    uint8_t temp = 0;
    if (DL_GPIO_readPins(KEY_PORT, KEY_S1_PIN)) temp = 1;
    // if (!DL_GPIO_readPins(KEY_PORT, KEY_S2_PIN)) temp = 2;
    if (DL_GPIO_readPins(KEY_PORT, KEY_S5_PIN)) temp = 5;
    // if (!DL_GPIO_readPins(KEY_PORT, KEY_K2_PIN)) temp = 3;

    return temp;
}
